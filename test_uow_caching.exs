#!/usr/bin/env elixir

# Test script to verify UoW caching behavior
Mix.install([{:drops, path: "."}])

defmodule TestOperations do
  use Drops.Operations, repo: nil
end

defmodule TestCommand do
  use TestOperations, type: :command

  schema do
    %{
      required(:name) => string()
    }
  end

  @impl true
  def execute(params) do
    {:ok, params}
  end
end

# Test that UoW is cached
IO.puts("Testing UoW caching...")

# Get UoW multiple times and check if they're the same object
uow1 = TestCommand.__unit_of_work__()
uow2 = TestCommand.__unit_of_work__()
uow3 = TestCommand.__unit_of_work__()

IO.puts("UoW1 object_id: #{:erlang.phash2(uow1)}")
IO.puts("UoW2 object_id: #{:erlang.phash2(uow2)}")
IO.puts("UoW3 object_id: #{:erlang.phash2(uow3)}")

if uow1 === uow2 and uow2 === uow3 do
  IO.puts("✅ SUCCESS: UoW is properly cached - same object returned on multiple calls")
else
  IO.puts("❌ FAILURE: UoW is not cached - different objects returned")
end

# Test that operations still work correctly
IO.puts("\nTesting operation functionality...")
case TestCommand.call(%{name: "test"}) do
  {:ok, %{result: %{name: "test"}}} ->
    IO.puts("✅ SUCCESS: Operation works correctly with cached UoW")
  other ->
    IO.puts("❌ FAILURE: Operation failed: #{inspect(other)}")
end
