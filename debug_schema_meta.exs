#!/usr/bin/env elixir

# Debug script to check schema metadata
Mix.install([{:drops, path: "."}])

defmodule TestOperations do
  use Drops.Operations, repo: Drops.TestRepo
end

# Test regular schema
defmodule TestRegularSchema do
  use TestOperations, type: :command

  schema do
    %{
      required(:name) => string()
    }
  end

  @impl true
  def execute(params) do
    {:ok, params}
  end
end

# Test Ecto schema
defmodule TestEctoSchema do
  use TestOperations, type: :command

  schema(Test.Ecto.UserSchema)

  @impl true
  def execute(params) do
    {:ok, params}
  end
end

# Test merged schema
defmodule TestMergedSchema do
  use TestOperations, type: :command

  schema(Test.Ecto.UserSchema) do
    %{
      optional(:extra_field) => string()
    }
  end

  @impl true
  def execute(params) do
    {:ok, params}
  end
end

IO.puts("=== Schema Metadata Debug ===")

IO.puts("\n1. Regular Schema:")
regular_meta = Module.get_attribute(TestRegularSchema, :schema_meta, %{})
IO.inspect(regular_meta, label: "Regular schema meta")

IO.puts("\n2. Ecto Schema:")
ecto_meta = Module.get_attribute(TestEctoSchema, :schema_meta, %{})
IO.inspect(ecto_meta, label: "Ecto schema meta")

IO.puts("\n3. Merged Schema:")
merged_meta = Module.get_attribute(TestMergedSchema, :schema_meta, %{})
IO.inspect(merged_meta, label: "Merged schema meta")

IO.puts("\n=== UoW Debug ===")

IO.puts("\n1. Regular Schema UoW:")
regular_uow = TestRegularSchema.__unit_of_work__()
IO.inspect(regular_uow.steps, label: "Regular UoW steps")

IO.puts("\n2. Ecto Schema UoW:")
ecto_uow = TestEctoSchema.__unit_of_work__()
IO.inspect(ecto_uow.steps, label: "Ecto UoW steps")

IO.puts("\n3. Merged Schema UoW:")
merged_uow = TestMergedSchema.__unit_of_work__()
IO.inspect(merged_uow.steps, label: "Merged UoW steps")
